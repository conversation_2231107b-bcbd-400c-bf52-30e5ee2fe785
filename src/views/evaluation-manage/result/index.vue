<template>
  <page-wrapper route-name="evaluation-reslut::">
    <div class="result">
      <el-card class="table-card">
        <div class="evaluation-task-table">
          <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData"
            :transformListData="transformListData" :query="query">
            <template #query>
              <div class="flexBetweenStart">
                <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
                  @reset="events.reset" />

                  <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import { assign } from "lodash";
const { $router, proxy, $app } = useCtx();
import * as evalManageApi from "@/api/eval-manage";
import * as commonApi from "@/api/common";

const routeQuery = $app.$route.query;
const query = ref<any>({});
const columns = ref<any[]>([]);

const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "标注人 或 Query 或 策略名称",
    },
  },

  time: {
    type: "datetimerange",
    label: "",
    modelValue: "",
    width: "250px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});

//列配置
const defaultColumns = ref([
  {
    prop: "sceneProcessName",
    label: "策略名称",
    minWidth: 240,
  },
  {
    prop: "regionName",
    label: "环境",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    minWidth: 190,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        const routeInfo = {
          name: `mark-index`,
          query: {
            markMode: "history",
            markRecordId: record.markRecordId,
          },
        };
        const resolvedRoute = $router.resolve(routeInfo);
        window.open(resolvedRoute.href, "_blank");
      },
    },
  },
  {
    prop: "account",
    label: "标注人",
    minWidth: 120,
  },
  {
    prop: "createdDateRender",
    label: "标注时间",
    minWidth: 180,
  },
  {
    prop: "traceId",
    label: "traceId",
    minWidth: 180,
  },
  {
    prop: "url",
    label: "URL",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        window.open(record.url, "_blank")
      },
    },
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 200,
  },
]);

//列表查询
const handleParams = (params: any) => {
  if (params.time) {
    params.startTime = params.time[0]
    params.endTime = params.time[1]
    delete params.time
  }
  return params
}

const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = handleParams(data)

    evalManageApi.getTablePage({ ...params, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName, type: 1 }).then((result) => {
      const arr = util.generateTableColumns(result.content);
      columns.value = defaultColumns.value.concat([...arr] as any);
      result.content = result.content.map((item: any) => ({
        ...item,
        ...item.extendFieldMap,
      }));
      resolve(result);
    });
  });
};

//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.docIdxRender = (x.docIdx != undefined && x.type === 0) ? `Top${x.docIdx + 1}` : "-";
    return x;
  });
};

//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: () => { },
  exportExcel: () => {
    evalManageApi
      .getTableExport({ column: routeQuery.column, userType: routeQuery.userType, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName, type: 1 })
      .then((res) =>
        util.downloadFile(
          res,
          `query标注明细.xlsx`
        )
      );
  },
});

const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};

const anasisList = ref([]);
// 场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  anasisList.value = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

//初始化
onMounted(async () => {
  getAnasisList();
});

//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.result {
  padding: 10px;

  .table-card {
    height: calc(100vh - 120px);

    .el-card__body {
      height: 100%;
    }

    .no-bottom {
      margin-bottom: 0;
    }
  }
}
.evaluation-task-table {
  height: calc(100% - 40px);

  ::v-deep {
    .query-wrapper {
      padding: 0 !important;
    }

    .table-wrapper {
      padding: 0 !important;
    }
  }
}

::v-deep(.el-radio-group) {
  margin-bottom: 14px;
}

.total-info,
.inline-block {
  display: inline-block;
}

.name+.name {
  margin-left: 15px;
}
</style>