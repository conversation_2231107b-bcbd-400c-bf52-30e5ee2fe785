<template>
  <page-wrapper :route-name="`mark-index::`">
    <div class="mark-index">
      {{ modelValue.markRecordId }}
      <el-row class="first-row">
        <el-col :span="24">
          <div class="flexBetween">
            <div class="flex" style="flex-grow: 1">
              <div class="flex">
                <strong style="width: 60px">我的任务</strong>
                <el-select v-model="missionId" @change="events.missionChange" placeholder="请选择测评任务" filterable
                  style="width: 180px">
                  <el-option v-for="item in myMissionList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </div>

              <div>
                <span style="white-space: nowrap">完成进度：{{ complatePercentage }}</span>
              </div>

              <div style="flex-grow: 1">
                <el-input v-model="modelValue.query" disabled="true" placeholder="请输入你要测评的query"
                  :prefix-icon="icons.Search" @keydown.enter="events.doQuery">
                  <template #append>
                    <el-button @click="events.doQuery">搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div style="min-width: 200px">
                <el-button :icon="icons.ArrowLeftBold" @click="events.preQuery" :disabled="modelValue.preDisabled"
                  style="color: black">
                  上一条
                </el-button>
                <el-button :icon="icons.ArrowRightBold" @click="events.nextQuery" :disabled="modelValue.nextDisabled"
                  style="color: black">
                  下一条
                </el-button>
              </div>
            </div>
            <el-dropdown trigger="click">
              <el-button type="primary" style="margin-left: 12px">更多操作</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="icons.Open" @click="modelValue.displayScore = true"
                    :disabled="modelValue.displayScore == true">
                    显示策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.TurnOff" @click="modelValue.displayScore = false"
                    :disabled="modelValue.displayScore == false">
                    隐藏策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowDownBold" @click="events.unfoldContent"
                    :disabled="dataC.isEmpty(modelValue.searchResult)">
                    展开content
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowUpBold" @click="events.foldContent"
                    :disabled="dataC.isEmpty(modelValue.searchResult)">
                    折叠content
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button type="primary" :icon="icons.Document" style="margin-left: 5px;" @click="saveMarkResult(false)">保存</el-button>
          </div>
        </el-col>
      </el-row>

      <el-row class="second-row">
        <el-switch v-model="modelValue.isQueryIgnore" inactive-text="是否弃标"></el-switch>
        <div v-if="modelValue.isQueryIgnore" class="ignore-section">
          <el-collapse v-model="ignoreCollapseActive" class="ignore-collapse">
            <el-collapse-item title="弃标原因" name="ignore-reason" class="ignore-collapse-item">
              <template #title>
                <div class="ignore-title">
                  <el-icon class="ignore-icon"><Warning /></el-icon>
                  <span>弃标原因</span>
                </div>
              </template>
              <div class="ignore-content">
                <AnnotationTreeComponent :model-value="modelValue.queryIgnore.ignore"
                  @update:modelValue="handleQueryIgnoreChange" />
              </div>
            </el-collapse-item>
          </el-collapse>
          <div class="ignore-remark">
            <el-input v-model="modelValue.queryIgnore.remark" type="textarea"
              placeholder="请输入弃标备注（可选）" :rows="3" maxlength="500" show-word-limit></el-input>
          </div>
        </div>
      </el-row>

      <el-row style="position: relative; flex: 1; min-height: 0;">
        <el-tabs v-model="modelValue.activeName" @tab-change="events.tabChange" tab-position="top"
          style="width: 100%; height: 100%;">
          <el-tab-pane label="搜索结果" name="searchPane" style="height: 100%;">
            <my-empty v-if="dataC.isEmpty(modelValue.docList)" :size="120" class="tab-content-empty" />
            <div v-else class="tab-content-scrollable">
              <DocAnnotation v-for="(item, index) in modelValue.docList || []" ref="docAnnotationRef" :key="index"
                :mark-record-id="modelValue.markRecordId" :strategy-id="item.strategyId" :target-id="item.targetId"
                :doc-data="item.doc" :doc-index="index" :score-ranks="item.scoringRanks"
                :display-score="modelValue.displayScore" :standard-config="modelValue.currentMission?.standardConfig"
                :extend-fields="modelValue.currentMission?.extendFields" :annotation-data="bindMarkValue2DimsForm(item)"
                @update:annotationData="(newValue: any) => handleDocAnnotationChange(index, newValue)"
                :is-ignore="item.ignore"
                :disabled="!modelValue.isQueryIgnore"
                @update:isIgnore="(newValue: any) => handleDocIgnoreStatusChange(index, newValue)" :remark="item.remark"
                @update:remark="(newValue: any) => handleRemarkChange(index, newValue)" style="margin-bottom: 16px" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="Chat效果" name="chatPane" style="height: 100%;">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="tab-content-empty" />

            <div v-else class="tab-content-scrollable">
              <div v-for="(strategy, index) in modelValue.searchResult" :key="strategy.strategyId"
                class="chat-card-wrapper">
                <el-card class="chat-card">
                  <div class="chat-content-wrapper">
                    <!-- 左侧：Chat展示区域 (70%) -->
                    <div class="chat-display-section">
                      <!-- Chat内容区域 -->
                      <div class="chat-content-area">
                        <div class="chat-index-badge">{{ index + 1 }}</div>
                        <MarkChat :chat="strategy.chat" :recallInfo="strategy.recallList"
                          @reloadChat="events.reloadChat(strategy.strategyId)"></MarkChat>
                      </div>
                    </div>

                    <!-- 右侧：策略信息区域 (30%) -->
                    <div class="strategy-section">
                      <div class="strategy-header">
                        <h4>标注信息</h4>
                      </div>
                      <div class="chat-field evaluation-field" v-if="strategy.chat?.markResult">
                        <AnnotationTreeComponent :model-value="strategy.chat?.markResult.feedbackList"
                        @update:modelValue="(newValue: any) => handleChatAnnotationChange(index, newValue)"/>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-row>

    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, onMounted, watch } from "vue";
import { dataC } from "turing-plugin";
import * as icons from "@element-plus/icons-vue";
import { Warning } from "@element-plus/icons-vue";
import * as missionApi from "@/api/eval-task";
import * as markApi from "@/api/eval-mark";
import DocAnnotation from "./DocAnnotation.vue";
import MarkChat from "./MarkChat.vue";
import AnnotationTreeComponent from "./AnnotationTreeComponent.vue";
import useCtx from "@/hooks/useCtx";

const { $app, proxy, $router } = useCtx();

const missionId = ref("");
const ignoreCollapseActive = ref<string[]>([]);

const myMissionList = ref<any[]>([]);
const modelValue = reactive({
  activeName: "searchPane",
  currentMissionId: "",
  currentMission: {} as any,
  isQueryIgnore: false,
  queryIgnore: { ignore: [] as any[], remark: "" },
  markRecordId: "",
  query: "",
  searchResult: [] as any[],
  displayScore: false,
  preDisabled: false,
  nextDisabled: false,
  docList: [] as any[],
});

const complatePercentage = computed(() => {
  modelValue.currentMission = myMissionList.value.find(
    (item) => item.id == modelValue.currentMissionId
  );
  return modelValue.currentMission
    ? `${(
      (modelValue.currentMission.completeQueryCount /
        modelValue.currentMission.assignQueryCount) *
      100
    ).toFixed(0)}%`
    : "-";
});

// 监听弃标状态变化
watch(() => modelValue.isQueryIgnore, (newValue) => {
  if (newValue) {
    // 当开启弃标时，自动展开折叠面板
    ignoreCollapseActive.value = ['ignore-reason'];
  } else {
    // 当关闭弃标时，收起折叠面板
    ignoreCollapseActive.value = [];
  }
});

const bindMarkValue2DimsForm = (docItem: any) => docItem.dimsList || [];

const saveMarkResult = async (completeCheck: boolean) => {
  modelValue.docList.forEach((item) => {
    const recall =
      modelValue.searchResult[item.targetIdx].recallList[item.docIdx];
    recall.markResult = recall.markResult || { dimsList: [] };
    recall.markResult.dimsList = item.dimsList;
    recall.markResult.ignore = item.ignore;
    recall.markResult.remark = item.remark;
  });

  const result = await markApi.doSave(
    getSaveRequest(),
    modelValue.currentMissionId,
    completeCheck
  );
  if (!completeCheck) {
    $app.$message.success("保存成功");
    return true;
  }
  return (
    result.data.complete || ($app.$message.warning(result.data.msg), false)
  );
};

const getSaveRequest = () => ({
  id: modelValue.markRecordId,
  targets: modelValue.searchResult.map((strategy) => ({
    targetId: strategy.targetId,
    recallList: strategy.recallList.map((recall, index) => ({
      resultId: recall.markResult?.resultId || null,
      docIdx: index,
      url: recall.doc.url,
      title: recall.doc.title,
      markTargetId: strategy.targetId,
      ascribeList: recall.markResult?.ascribeList,
      dimsList: recall.markResult?.dimsList,
      ignore: recall.markResult?.ignore,
      remark: recall.markResult?.remark,
    })),
    chat: strategy.chat.markResult || null,
  })),
  queryIgnore: modelValue.isQueryIgnore ? modelValue.queryIgnore : null,
});

/**
 * 初始化chat标注信息
 */
function initChat(){
  modelValue.searchResult.forEach((strategy) => {
      //如果chat标注结果为空 则添加默认标注值
      if(dataC.isEmpty(strategy.chat) || dataC.isEmpty(strategy.chat.markResult) || dataC.isEmpty(strategy.chat.markResult.feedbackList)){
        strategy.chat = {...strategy.chat, markResult:{resultId:null, feedbackList:modelValue.currentMission?.standardConfig.chat}}
      }
  })
}

const render = async (result: any) => {
  console.log("开始渲染数据:", result);
  try {
    modelValue.docList = [];
    modelValue.searchResult = result.data.targets;
    modelValue.query = result.data.query;
    modelValue.currentMissionId = result.data.missionId;
    missionId.value = result.data.missionId;

    // 更新当前任务对象
    modelValue.currentMission = myMissionList.value.find(
      (item) => item.id == modelValue.currentMissionId
    ) || {};

    initChat();

    modelValue.isQueryIgnore = result.data.queryIgnore ? true : false;
    modelValue.queryIgnore = result.data.queryIgnore || {
      ignore: modelValue.currentMission?.standardConfig?.queryIgnore || [],
      remark: "",
    };
    modelValue.markRecordId = result.data.id;

    console.log("基础数据设置完成:", {
      query: modelValue.query,
      missionId: modelValue.currentMissionId,
      markRecordId: modelValue.markRecordId,
      targetsCount: result.data.targets?.length || 0
    });

    result.data.targets?.forEach((target, index) => {
      target.recallList?.forEach((doc, docIdx) => {
        modelValue.docList.push({
          targetIdx: index,
          docIdx: docIdx,
          doc: doc.doc,
          scoringRanks: doc.scoringRanks,
          dimsList: doc.markResult?.dimsList || [],
          ignore: doc.markResult?.ignore || false,
          remark: doc.markResult?.remark || "",
          targetId: target.targetId,
          strategyId: target.strategyId,
        });
      });
    });

    console.log("文档列表构建完成:", {
      docListLength: modelValue.docList.length,
      docList: modelValue.docList
    });
  } catch (error) {
    console.error("渲染数据失败:", error);
    throw error;
  }
};

const initModelValue = () => {
  modelValue.docList = [];
  modelValue.markRecordId = "";
  modelValue.searchResult = [];
  modelValue.query = "";
  modelValue.isQueryIgnore = false;
  modelValue.queryIgnore = { ignore: [], remark: "" };
};

const events = {
  missionChange: async () => {
    if (!dataC.isEmpty(modelValue.markRecordId)) {
      try {
        await $app.$confirm({ title: `此操作将切换任务,是否继续?` });
        events.switchMission();
      } catch {
        missionId.value = dataC.isEmpty(modelValue.currentMissionId)
          ? ""
          : modelValue.currentMissionId;
      }
    } else {
      events.switchMission();
    }
  },
  switchMission: async () => {
    modelValue.currentMissionId = missionId.value;
    //清空历史数据
    initModelValue();
    await findUnfinished();

    //如果没有未标注完成的记录  则自动获取下一条记录
    if (dataC.isEmpty(modelValue.markRecordId)) {
      await events.switchQuery();
    }
  },
  doQuery: async () => {
    modelValue.docList = [];
    modelValue.markRecordId = "";
    modelValue.searchResult = [];
    modelValue.isQueryIgnore = false;

    const result = await markApi.createMarkRecord({
      query: modelValue.query,
      missionId: modelValue.currentMissionId,
    });
    modelValue.markRecordId = result.data;
    await events.findByMarkRecordId(modelValue.markRecordId);
  },
  findByMarkRecordId: async (markRecordId: string) => {
    console.log("开始查找标注记录:", markRecordId);
    try {
      const result = await markApi.getMarkReocordById(
        markRecordId,
        true,
        false,
        true
      );
      console.log("标注记录查找结果:", result);
      await render(result);
      console.log("标注记录渲染完成");
    } catch (error) {
      console.error("查找标注记录失败:", error);
      throw error;
    }
  },
  preQuery: async () => {
    if (!(await saveMarkResult(true))) return;
    const result = await missionApi.myMissionQuery(
      modelValue.currentMissionId,
      modelValue.markRecordId,
      "prev"
    );
    if (result.data.recordId) {
      await events.findByMarkRecordId(result.data.recordId);
      modelValue.preDisabled = false;
      modelValue.nextDisabled = false;
    } else {
      $app.$message.warning("已经是第一条了!");
      modelValue.preDisabled = true;
    }
  },
  nextQuery: async () => {
    if (!(await saveMarkResult(true))) return;

    //更新任务列表
    myMissionList.value = (await missionApi.myMission()).data;

    events.switchQuery();
    modelValue.preDisabled = false;
    modelValue.nextDisabled = false;
  },
  switchQuery: async () => {
    const result = await missionApi.myMissionQuery(
      modelValue.currentMissionId,
      modelValue.markRecordId,
      "next"
    );
    if (result.data.recordId) {
      await events.findByMarkRecordId(result.data.recordId);
    } else if (result.data.query) {
      modelValue.query = result.data.query;
      await events.doQuery();
    } else {
      $app.$message.warning("已经是最后一条了!");
    }
  },
  unfoldContent: () =>
    modelValue.docList.forEach((item, index) =>
      proxy.$refs.docAnnotationRef[index].unfoldContent()
    ),
  foldContent: () =>
    modelValue.docList.forEach((item, index) =>
      proxy.$refs.docAnnotationRef[index].foldContent()
    ),
  tabChange: (tabPaneName: any) => {
    console.log("Tab changed to:", tabPaneName);
    // 可以在这里添加tab切换时的逻辑
    if (tabPaneName === "chatPane") {
      // Chat面板的逻辑
      console.log("切换到Chat面板");
    } else if (tabPaneName === "searchPane") {
      // 搜索结果面板的逻辑
      console.log("切换到搜索结果面板");
    }
  },
  reloadChat: async (strategyId: string) => {
        try {
      const result = await markApi.getMarkReocordById(modelValue.markRecordId, false, false, true);

      if (!dataC.isEmpty(strategyId)) {
        const response = result.data.targets.find((item: SearchResult) => item.strategyId === strategyId);
        const searchResult = modelValue.searchResult.find((item: SearchResult) => item.strategyId === strategyId);
        if (response && searchResult) {
          searchResult.chat = response.chat;
        }
      }

      initChat();
    } catch (error) {
      console.error("Failed to reload chat:", error);
      $app.$message.error("重新加载聊天失败");
    }
  },
};

const handleChatAnnotationChange = (index: number, newValue: any[]) => {
  modelValue.searchResult[index].chat.markResult.feedbackList = newValue;
}
const handleQueryIgnoreChange = (newValue: any[]) =>
  (modelValue.queryIgnore.ignore = newValue);
const handleDocAnnotationChange = (index: number, newValue: any) =>
  (modelValue.docList[index].dimsList = newValue);
const handleDocIgnoreStatusChange = (index: number, newValue: boolean) =>
  (modelValue.docList[index].ignore = newValue);
const handleRemarkChange = (index: number, newValue: string) =>
  (modelValue.docList[index].remark = newValue);

const findUnfinished = async () => {
  const result = await markApi.findUnfinished(modelValue.currentMissionId);
  if (dataC.isEmpty(result.data)) return;

  try {
    await $app.$confirm({ title: `恢复任务?` });
    await render(result);
  } catch {
    // 不需要操作，后端会处理
  }
};

onMounted(async () => {
  try {
    //1.获取所有任务
    myMissionList.value = (await missionApi.myMission()).data;

    //2.获取所有的路由中的任务id 和 测评记录id（存在表示为历史标注回显）
    let chicenMissionId = $router.currentRoute.value.query.missionId as string;
    let markRecordId = $router.currentRoute.value.query.markRecordId as string;

    if (markRecordId) {
      console.log("历史标注回显, markRecordId:", markRecordId);
      try {
        await events.findByMarkRecordId(markRecordId);
        console.log("历史标注回显成功");
      } catch (error) {
        console.error("历史标注回显失败:", error);
        $app.$message.error("加载历史标注数据失败");
      }
    } else {
      console.log("非历史标注模式");

      if (myMissionList.value.length && dataC.isEmpty(chicenMissionId)) {
        chicenMissionId = myMissionList.value[0].id;
        console.log("使用默认任务ID:", chicenMissionId);
        await findUnfinished();
      }

      if (!myMissionList.value.length) {
        console.log("没有测评任务");
        $app.$message.warning("没有可用的测评任务");
        return;
      }

      modelValue.currentMissionId = chicenMissionId;
      missionId.value = chicenMissionId;
      console.log("设置当前任务ID:", chicenMissionId);

      //如果没有未标注完成的记录  则自动获取下一条记录
      if (dataC.isEmpty(modelValue.markRecordId)) {
        console.log("开始获取下一条记录");
        await events.switchQuery();
      }
    }

    console.log("MarkIndex onMounted 完成");
  } catch (error) {
    console.error("MarkIndex onMounted 出错:", error);
    $app.$message.error("页面初始化失败");
  }
});
</script>

<style lang="scss" scoped>
.mark-index {
  display: flex;
  flex-direction: column;
  padding: 8px 15px;
  line-height: 23px;
  overflow: hidden;
  height: calc(100vh - 60px); // 设置页面高度，减去顶部导航等空间

  .first-row {
    flex: 0 0 40px;
    padding-bottom: 8px;
  }

  .second-row {
    flex: 0 0 auto;
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .ignore-section {
      width: 100%;
      margin-top: 12px;

      .ignore-collapse {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        .ignore-collapse-item {
          border: none;

          ::v-deep .el-collapse-item__header {
            background: linear-gradient(135deg, #fef7e0 0%, #fff2cc 100%);
            border: none;
            padding: 12px 16px;
            font-weight: 500;
            color: #e6a23c;

            &:hover {
              background: linear-gradient(135deg, #fdf2d5 0%, #ffecb3 100%);
            }

            .el-collapse-item__arrow {
              color: #e6a23c;
              font-weight: bold;
            }
          }

          ::v-deep .el-collapse-item__wrap {
            border: none;
            background: #fafafa;
          }

          ::v-deep .el-collapse-item__content {
            padding: 16px;
            background: #fafafa;
          }
        }

        .ignore-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .ignore-icon {
            color: #e6a23c;
            font-size: 16px;
          }

          .ignore-tag {
            margin-left: auto;
            font-size: 11px;
            padding: 2px 6px;
          }
        }

        .ignore-content {
          background: white;
          border-radius: 4px;
          padding: 12px;
          border: 1px solid #e4e7ed;
          margin-bottom: 12px;
        }
      }

      .ignore-remark {
        margin-top: 12px;

        ::v-deep .el-textarea {
          .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;

            &:focus {
              border-color: #e6a23c;
              box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.1);
            }
          }
        }
      }
    }
  }

  // 优化的滚动容器样式
  .tab-content-scrollable {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    padding: 8px 0;
    // 移除固定高度，让容器自适应
  }

  .tab-content-empty {
    flex: 1;
    min-height: 200px; // 设置最小高度，避免过小
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // Chat 卡片样式 - 与 DocAnnotation 保持一致
  .chat-card-wrapper {
    margin-bottom: 16px;

    .chat-card {
      .chat-content-wrapper {
        display: flex;
        gap: 16px;

        // 左侧 Chat 展示区域 (75%)
        .chat-display-section {
          flex: 1;
          display: flex;
          flex-direction: column;

          // Chat 内容区域
          .chat-content-area {
            flex: 1;
            min-height: 0;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 16px;
            background: #fafafa;
            position: relative;

            // 序号徽章 - 左上角
            .chat-index-badge {
              position: absolute;
              top: -11px;
              left: -9px;
              background: #409eff;
              color: white;
              border-radius: 4px;
              font-size: 12px;
              font-weight: bold;
              padding: 4px 8px;
              text-align: center;
              z-index: 10;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            ::v-deep .mark-chat {
              padding-bottom: 0;
              .vuepress-markdown-body {
                background: transparent;
                font-size: 13px;
                line-height: 1.6;
              }
            }
          }
        }

        // 右侧策略信息区域 (25%)
        .strategy-section {
          flex: 0 0 25%;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .strategy-header {
            background: #f5f7fa;
            padding: 8px 12px;
            border-bottom: 1px solid #e4e7ed;
            flex-shrink: 0;

            h4 {
              margin: 0;
              font-size: 14px;
              color: #606266;
            }
          }

          .strategy-content {
            flex: 1;
            padding: 12px;
            overflow-x: auto;
            overflow-y: auto;

            .strategy-field {
              display: flex;
              margin-bottom: 8px;
              font-size: 13px;
              min-width: fit-content;

              &:last-child {
                margin-bottom: 0;
              }

              label {
                flex: 0 0 80px;
                color: #606266;
                font-weight: 500;
                white-space: nowrap;
              }

              span {
                flex: 1;
                color: #303133;
                word-break: break-all;
                min-width: 0;
              }
            }
          }

          // 评价选项区域样式
          .chat-field.evaluation-field {
            border: none;
            padding: 12px;
            overflow-x: auto;
            overflow-y: auto;
            flex: 1;

            .field-content {
              padding: 0;
              min-width: fit-content;

              .evaluation-content {
                display: flex;
                flex-direction: column;
                gap: 8px;
                min-width: fit-content;

                .chat-checkbox-group {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  min-width: fit-content;

                  .el-checkbox {
                    margin-right: 0;
                    white-space: nowrap;

                    ::v-deep .el-checkbox__label {
                      white-space: nowrap;
                    }
                  }
                }
              }
            }

            // 自定义滚动条样式
            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;

              &:hover {
                background: #a8a8a8;
              }
            }
          }

          // 为整个策略区域添加滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }

  // 保留原有的样式以兼容
  .doc-annotation-list {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
  }

  ::v-deep .el-input-group__append {
    background-color: var(--el-color-primary);
    color: #fff;
  }

  .flexBetween {
    display: flex;
    justify-content: space-between;

    >.flex {
      div+div {
        margin-left: 12px;
      }
    }
  }

  .searchPane {
    height: calc(100vh - 290px);

    ::v-deep .el-col {
      height: 100%;

      .mark-pane {
        height: 100%;
        padding: 0 6px;
        overflow-y: auto;
      }
    }
  }

  .chatPane {
    height: calc(100vh - 290px);

    ::v-deep .el-col {
      height: 100%;

      .chat-pane {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: 2px 3px;
      }
    }
  }

  .chat-content {
    height: calc(100vh - 290px);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  // 强制 tabs 头部在上方并优化布局
  ::v-deep .el-tabs {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
  }

  ::v-deep .el-tabs__header {
    padding: 0 0 8px 0;
    flex-shrink: 0;
    margin-bottom: 0;
    order: -1; // 确保头部在最前面
  }

  ::v-deep .el-tabs__nav-wrap {
    padding: 0;
  }

  ::v-deep .el-tabs__content {
    padding: 0;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    order: 1; // 确保内容在头部之后
    overflow: hidden; // 防止内容溢出
  }

  ::v-deep .el-tab-pane {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出
  }
}

// Chat 卡片响应式设计
@media (max-width: 1200px) {
  .mark-index .chat-card-wrapper .chat-card .chat-content-wrapper {
    flex-direction: column;

    .chat-display-section {
      flex: 1;
    }

    .strategy-section {
      flex: 0 0 auto;
      min-height: 150px;
    }
  }
}

::v-deep .el-dropdown-menu__item {
  flex-direction: row !important;
  align-items: center !important;
}
</style>
